#!/bin/bash

# Git 提交命令脚本 - 完整的富文本标签页系统实现
# 包含所有27个文件的变更

echo "🚀 开始提交富文本标签页系统实现 (27个文件)..."

# 1. 添加核心富文本功能 (2个文件)
echo "🔧 添加核心富文本功能..."
git add src/components/rich-text-editor/RichTextEditor.tsx
git add src/hooks/useRichTextTabs.ts

# 2. 添加富文本标签页组件系统 (4个文件)
echo "📁 添加富文本标签页组件..."
git add src/components/rich-text-tabs/RichTextTabsManager.tsx
git add src/components/rich-text-tabs/RichTextTabsViewer.tsx
git add src/components/rich-text-tabs/README.md
git add src/components/rich-text-tabs/index.ts

# 3. 添加 UI 组件库 (2个文件)
echo "🎨 添加 UI 组件库..."
git add src/components/ui/breadcrumb.tsx
git add src/components/ui/news-card.tsx

# 4. 添加文档和示例 (4个文件)
echo "📚 添加文档和示例..."
git add docs/design-guide.md
git add rich-text-migration-guide.md
git add src/app/design-showcase/page.tsx
git add save.log

# 5. 添加 API 层更新 (4个文件)
echo "🔌 添加 API 层更新..."
git add openapi.json
git add src/api/generated/ayafeedComponents.ts
git add src/api/generated/ayafeedContext.ts
git add src/types/api-types.d.ts

# 6. 添加系统集成改进 (8个文件)
echo "🏗️ 添加系统集成改进..."
git add src/components/admin/sidebar.tsx
git add src/components/admin/MultilingualEventForm.tsx
git add src/components/events/EventDetailTabs.tsx
git add src/components/rich-text-editor/MultilingualContentManager.tsx
git add src/components/BetterAuthRoleGuard.tsx
git add src/components/RoleGuard.tsx
git add src/contexts/user.tsx
git add src/app/globals.css

# 7. 添加工具文件 (3个文件)
echo "🛠️ 添加工具文件..."
git add COMMIT_MESSAGE.md
git add git-commit-commands.sh
git add git-commit-simple.sh

# 7. 执行提交
echo "💾 执行提交..."
git commit -m "fix(rich-text): 修复富文本编辑器内容显示问题并完善标签页功能

🐛 问题修复:
- 修复富文本编辑器内容不显示问题
- 添加 JSON 字符串内容解析功能
- 修复 useRichTextTabs hook 中数据结构不匹配问题
- 统一数据提取逻辑，确保从 tabsData.data.tabs 正确获取内容

✨ 功能完善:
- 新增富文本标签页系统 (RichTextTabsManager, RichTextTabsViewer)
- 新增 useRichTextTabs hook 提供完整的标签页数据管理
- 支持多语言内容管理和语言切换功能
- 添加面包屑导航和新闻卡片 UI 组件

🔧 技术改进:
- 添加 parseContent 函数智能处理多种内容格式
- 改进编辑器内容更新逻辑，避免无限循环
- 优化本地状态与服务器数据的同步机制
- 添加完整的错误处理和向后兼容性支持

📁 主要变更:
- src/components/rich-text-editor/RichTextEditor.tsx
- src/hooks/useRichTextTabs.ts
- src/components/rich-text-tabs/ (新增)
- src/components/ui/ (新增组件)
- docs/design-guide.md (新增)

✅ 测试验证: 富文本编辑器正确显示内容，保存功能正常，多标签页功能完整"

echo "✅ 提交完成！"
echo ""
echo "📋 提交摘要:"
echo "   - 修复了富文本编辑器内容显示问题"
echo "   - 添加了完整的富文本标签页功能"
echo "   - 改进了内容格式处理和状态管理"
echo "   - 新增了多个 UI 组件和文档"
echo ""
echo "🔍 可以使用以下命令查看提交详情:"
echo "   git show --stat"
echo "   git log --oneline -1"
