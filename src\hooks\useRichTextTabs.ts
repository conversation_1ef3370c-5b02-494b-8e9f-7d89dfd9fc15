'use client';

import { useState, useCallback, useMemo, useEffect } from 'react';
import { useQueryClient, skipToken } from '@tanstack/react-query';
import { toast } from 'sonner';
import { useLocale } from 'next-intl';
import {
  useGetRichtexttabsConfigsEntityTypeLanguageCode,
  useGetRichtexttabsConfigsEntityTypeLanguageCodeAll,
  useGetRichtexttabsTabsEntityTypeEntityIdLanguageCode,
  usePostRichtexttabsContent,
  usePostRichtexttabsContentBatch,
  type GetRichtexttabsConfigsEntityTypeLanguageCodeResponse,
  type GetRichtexttabsConfigsEntityTypeLanguageCodeAllResponse,
} from '@/api/generated/ayafeedComponents';

export type EntityType = 'event' | 'venue';
export type LanguageCode = 'en' | 'zh' | 'ja';

export interface TabConfig {
  id: string;
  entity_type: "event" | "venue";
  language_code: "en" | "zh" | "ja";
  key: string;
  label: string;
  placeholder?: string;
  icon?: string;
  sort_order: number;
  is_active: boolean;
  is_preset: boolean;
  deleted_at?: string;
  deleted_by?: string;
  created_at: string;
  updated_at: string;
}

export interface TabContent {
  config: TabConfig;
  content?: string;
}

export interface UseRichTextTabsOptions {
  entityType: EntityType;
  entityId: string;
  languageCode?: LanguageCode;
  autoSave?: boolean;
  autoSaveDelay?: number;
  isAdminMode?: boolean; // 是否为管理模式，获取所有配置（包括已删除的）
}

export interface UseRichTextTabsReturn {
  // 数据状态
  configs: TabConfig[];
  tabs: TabContent[];
  isLoading: boolean;
  isConfigsLoading: boolean;
  isTabsLoading: boolean;
  error: any;
  
  // 当前状态
  currentLanguage: LanguageCode;
  activeTabKey: string | null;
  
  // 内容操作
  getTabContent: (tabKey: string) => string;
  updateTabContent: (tabKey: string, content: string) => void;
  
  // 保存操作
  saveTab: (tabKey: string) => Promise<void>;
  saveAllTabs: () => Promise<void>;
  isSaving: boolean;
  
  // 语言切换
  switchLanguage: (language: LanguageCode) => void;
  
  // 标签页操作
  setActiveTab: (tabKey: string) => void;
  
  // 状态检查
  hasUnsavedChanges: boolean;
  getUnsavedTabs: () => string[];
}

export function useRichTextTabs({
  entityType,
  entityId,
  languageCode,
  autoSave = false,
  autoSaveDelay = 2000,
  isAdminMode = false,
}: UseRichTextTabsOptions): UseRichTextTabsReturn {
  const locale = useLocale() as LanguageCode;
  const currentLanguage = languageCode || locale;
  const queryClient = useQueryClient();
  
  // 本地状态
  const [activeTabKey, setActiveTabKey] = useState<string | null>(null);
  const [localContent, setLocalContent] = useState<Record<string, string>>({});
  const [originalContent, setOriginalContent] = useState<Record<string, string>>({});
  
  // 获取标签页配置 - 普通模式
  const {
    data: configsDataNormal,
    isLoading: isConfigsLoadingNormal,
    error: configsErrorNormal,
  } = useGetRichtexttabsConfigsEntityTypeLanguageCode(
    isAdminMode ? skipToken : {
      pathParams: {
        entityType,
        languageCode: currentLanguage,
      },
    },
    {
      staleTime: 5 * 60 * 1000, // 5分钟缓存
    }
  );

  // 获取标签页配置 - 管理模式
  const {
    data: configsDataAdmin,
    isLoading: isConfigsLoadingAdmin,
    error: configsErrorAdmin,
  } = useGetRichtexttabsConfigsEntityTypeLanguageCodeAll(
    !isAdminMode ? skipToken : {
      pathParams: {
        entityType,
        languageCode: currentLanguage,
      },
    },
    {
      staleTime: 5 * 60 * 1000, // 5分钟缓存
    }
  );

  // 选择正确的数据
  const configsData = isAdminMode ? configsDataAdmin : configsDataNormal;
  const isConfigsLoading = isAdminMode ? isConfigsLoadingAdmin : isConfigsLoadingNormal;
  const configsError = isAdminMode ? configsErrorAdmin : configsErrorNormal;

  // 获取标签页内容
  const {
    data: tabsData,
    isLoading: isTabsLoading,
    error: tabsError,
  } = useGetRichtexttabsTabsEntityTypeEntityIdLanguageCode(
    {
      pathParams: {
        entityType,
        entityId,
        languageCode: currentLanguage,
      },
    },
    {
      staleTime: 2 * 60 * 1000, // 2分钟缓存
      enabled: !!entityId,
    }
  );

  // 使用 useEffect 来处理数据变化
  useEffect(() => {
    let actualTabs: any[] = [];

    // 检查数据结构，与 tabs useMemo 保持一致
    if (tabsData) {
      if ((tabsData as any).data && (tabsData as any).data.tabs) {
        actualTabs = (tabsData as any).data.tabs;
      } else if (tabsData.tabs) {
        actualTabs = tabsData.tabs;
      }
    }

    if (actualTabs.length > 0) {
      // 当数据加载成功时，初始化本地内容
      const contentMap: Record<string, string> = {};
      actualTabs.forEach((tab) => {
        if (tab.content) {
          contentMap[tab.config.key] = tab.content.content || '';
        }
      });

      setLocalContent(contentMap);
      setOriginalContent(contentMap);

      // 设置默认活跃标签页
      if (!activeTabKey && actualTabs.length > 0) {
        setActiveTabKey(actualTabs[0].config.key);
      }
    }
  }, [tabsData, activeTabKey]);
  
  // 保存操作的 mutations
  const saveContentMutation = usePostRichtexttabsContent({
    onSuccess: () => {
      toast.success('内容保存成功');
      // 刷新数据
      queryClient.invalidateQueries({
        queryKey: ['getRichtexttabsTabsEntityTypeEntityIdLanguageCode'],
      });
    },
    onError: (error) => {
      console.error('保存失败:', error);
      toast.error('保存失败，请重试');
    },
  });

  const saveBatchMutation = usePostRichtexttabsContentBatch({
    onSuccess: () => {
      toast.success('批量保存成功');
      // 刷新数据
      queryClient.invalidateQueries({
        queryKey: ['getRichtexttabsTabsEntityTypeEntityIdLanguageCode'],
      });
    },
    onError: (error) => {
      console.error('批量保存失败:', error);
      toast.error('批量保存失败，请重试');
    },
  });
  
  // 处理数据
  const configs = useMemo(() => {
    // API 返回的数据结构是 { code, message, data }，实际配置在 data 字段中
    if (configsData && typeof configsData === 'object' && 'data' in configsData) {
      return Array.isArray((configsData as any).data) ? (configsData as any).data : [];
    }

    return Array.isArray(configsData) ? configsData : [];
  }, [configsData]);

  const tabs = useMemo(() => {
    // 检查 tabsData 的结构
    if (!tabsData) return [];

    // 如果 tabsData 有 data 字段，使用 data.tabs
    if ((tabsData as any).data && (tabsData as any).data.tabs) {
      return (tabsData as any).data.tabs.map((tab: any) => ({
        config: tab.config,
        content: tab.content?.content || '',
      }));
    }

    // 否则直接使用 tabsData.tabs
    if (tabsData.tabs) {
      return tabsData.tabs.map((tab) => ({
        config: tab.config,
        content: tab.content?.content || '',
      }));
    }

    return [];
  }, [tabsData]);
  
  // 获取标签页内容
  const getTabContent = useCallback((tabKey: string): string => {
    return localContent[tabKey] || '';
  }, [localContent]);
  
  // 更新标签页内容
  const updateTabContent = useCallback((tabKey: string, content: string) => {
    setLocalContent(prev => ({
      ...prev,
      [tabKey]: content,
    }));
  }, []);
  
  // 保存单个标签页
  const saveTab = useCallback(async (tabKey: string) => {
    const content = localContent[tabKey] || '';

    await saveContentMutation.mutateAsync({
      body: {
        entity_type: entityType,
        entity_id: entityId,
        language_code: currentLanguage,
        content_type: tabKey,
        content,
      },
    });

    // 更新原始内容
    setOriginalContent(prev => ({
      ...prev,
      [tabKey]: content,
    }));
  }, [localContent, entityType, entityId, currentLanguage, saveContentMutation]);
  
  // 批量保存所有标签页
  const saveAllTabs = useCallback(async () => {
    const contents: Record<string, string> = {};

    // 只保存有变更的内容
    Object.keys(localContent).forEach(tabKey => {
      if (localContent[tabKey] !== originalContent[tabKey]) {
        contents[tabKey] = localContent[tabKey];
      }
    });

    if (Object.keys(contents).length === 0) {
      toast.info('没有需要保存的更改');
      return;
    }

    await saveBatchMutation.mutateAsync({
      body: {
        entity_type: entityType,
        entity_id: entityId,
        language_code: currentLanguage,
        contents,
      },
    });

    // 更新原始内容
    setOriginalContent(prev => ({
      ...prev,
      ...contents,
    }));
  }, [localContent, originalContent, entityType, entityId, currentLanguage, saveBatchMutation]);
  
  // 语言切换
  const switchLanguage = useCallback((language: LanguageCode) => {
    // 这里可以触发重新获取数据，但由于我们使用的是 currentLanguage，
    // 实际上需要父组件重新渲染来改变 languageCode
    console.log('切换语言到:', language);
  }, []);
  
  // 设置活跃标签页
  const setActiveTab = useCallback((tabKey: string) => {
    setActiveTabKey(tabKey);
  }, []);
  
  // 检查是否有未保存的更改
  const hasUnsavedChanges = useMemo(() => {
    return Object.keys(localContent).some(tabKey => 
      localContent[tabKey] !== originalContent[tabKey]
    );
  }, [localContent, originalContent]);
  
  // 获取有未保存更改的标签页
  const getUnsavedTabs = useCallback(() => {
    return Object.keys(localContent).filter(tabKey => 
      localContent[tabKey] !== originalContent[tabKey]
    );
  }, [localContent, originalContent]);
  
  return {
    // 数据状态
    configs,
    tabs,
    isLoading: isConfigsLoading || isTabsLoading,
    isConfigsLoading,
    isTabsLoading,
    error: configsError || tabsError,
    
    // 当前状态
    currentLanguage,
    activeTabKey,
    
    // 内容操作
    getTabContent,
    updateTabContent,
    
    // 保存操作
    saveTab,
    saveAllTabs,
    isSaving: saveContentMutation.isPending || saveBatchMutation.isPending,
    
    // 语言切换
    switchLanguage,
    
    // 标签页操作
    setActiveTab,
    
    // 状态检查
    hasUnsavedChanges,
    getUnsavedTabs,
  };
}
